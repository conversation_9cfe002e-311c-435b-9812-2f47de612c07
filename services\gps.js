const { listThings } = require('../modules/awsIot');
const { getLocationCollection } = require('../models/VesselLocation');
const { mqtt, iot, auth } = require('aws-iot-device-sdk-v2');
const { createLoggerWithPath } = require('../modules/winston');
const { calculateDistanceInMeters } = require('../utils/functions');
const io = require('../modules/io');
const Region = require('../models/Region');
const db = require('../modules/db');
const thingsboardService = require('../services/thingsboard.service');

const AWS_ACCESS_KEY_ID = process.env.AWS_ACCESS_KEY_ID
const AWS_SECRET_ACCESS_KEY = process.env.AWS_SECRET_ACCESS_KEY
const MQTT_CLIENT_ID = process.env.MQTT_CLIENT_ID
const MQTT_BROKER = process.env.MQTT_BROKER

const locationUpdateInterval = 60000 // in milliseconds

const clients = {}
// var subscribedThings = []

initializeClients()

setInterval(() => {
    console.log('[GPS Service] Reinitializing clients...');
    initializeClients()
}, 3600000);

function initializeClients() {
    Region.find({ is_live: true }).then((regions) => {
        regions.forEach(async (region) => {
            if (clients[region.value]) {
                console.log('[GPS Service] Already connected to region', region.value);
                return;
            }

            // const region = data.region;
            // const things = data.things.filter(
            //     (thing) => thing.thingTypeName === 'smartmast'
            // );

            // if (things.length === 0) return console.log(`No applicable things in region ${region}`);

            // const thingArns = things.map(t => t.thingArn)
            // if (thingArns.every(arn => subscribedThings.includes(arn))) return console.log('Already subscribed to things in region', region);

            // subscribedThings = subscribedThings.concat(thingArns)
            // console.log(subscribedThings)

            // const oldClient = clients[region]
            // if (oldClient) {
            //     oldClient.removeAllListeners()
            //     await oldClient.disconnect()
            // }

            const config = iot.AwsIotMqttConnectionConfigBuilder.new_with_websockets({
                region: region.value,
                credentials_provider: auth.AwsCredentialsProvider.newStatic(
                    AWS_ACCESS_KEY_ID,
                    AWS_SECRET_ACCESS_KEY
                ),
            })
                .with_clean_session(true)
                .with_client_id(`${MQTT_CLIENT_ID}-${region.value}`)
                .with_endpoint(`${MQTT_BROKER}.${region.value}.amazonaws.com`)
                .build();

            const client = new mqtt.MqttClient();
            const mqttClient = client.new_connection(config);

            try {
                await mqttClient.connect();
                clients[region.value] = mqttClient;
                console.log(`[GPS Service] Connected to MQTT Broker in region ${region.value}`);
                setupMqttClientListeners(mqttClient, region.value);
                // subscribeToGps(mqttClient, things, region);
                mqttClient.subscribe('#', mqtt.QoS.AtMostOnce)
            } catch (err) {
                console.error(`[GPS Service] Failed to connect to MQTT Broker in region ${region.value}: ${err}`);
            }
        });
    }).catch((err) => {
        console.error(`[GPS Service] FATAL ERROR: could not get live regions ${err}`);
    });

    if (process.env.NODE_ENV === 'prod') {
        listThings().then(async (regions) => {
            const sensorNames = regions.map(r => r.things.filter(e => e.thingTypeName).map(e => e.thingName)).flat();
            thingsboardService.verifySensors(sensorNames)
        }).catch((err) => {
            console.error(`[GPS Service] FATAL ERROR: could not get list things ${err}`);
        });
    }
}

function setupMqttClientListeners(mqttClient, region) {
    mqttClient.on('closed', () => {
        console.log(`[GPS Service] Connection closed for MQTT Broker in region ${region}`);
    });

    mqttClient.on('interrupt', (err) => {
        console.log(`[GPS Service] Received interrupt for connection to MQTT Broker in region ${region}: ${err}`);
    });

    mqttClient.on('resume', () => {
        console.log(`[GPS Service] Resumed connection to MQTT Broker in region ${region}`);
        // subscribeToGps(mqttClient, things, region);
        mqttClient.subscribe('#', mqtt.QoS.AtMostOnce)
    });

    mqttClient.on('disconnect', () => {
        console.log(`[GPS Service] FATAL ERROR: Disconnected from MQTT Broker in region ${region}`);
        delete clients[region];
        mqttClient.removeAllListeners();
        initializeClients();
    });

    mqttClient.on('error', (err) => {
        console.error(`[GPS Service] Received connection error for MQTT Broker in region ${region}: ${err}`);
    });

    mqttClient.on('message', async (topic, message) => {
        // console.log('[GPS Service] received message from topic', topic, 'in region', region)
        await processMessage(topic, message, region);
    });
}

// function subscribeToGps(mqttClient, things, region) {
//     things.forEach((thing) => {
//         const topic = `${thing.thingName}/gps/status`;
//         mqttClient
//             .subscribe(topic, mqtt.QoS.AtLeastOnce)
//             .then((res) => {
//                 if (res.error_code) {
//                     console.error(`Failed to subscribe to topic ${topic} in region ${region}: error_code ${res.error_code}`);
//                 } else {
//                     console.log(`Subscribed to topic ${topic} in region ${region}`);
//                 }
//             })
//             .catch((err) =>
//                 console.error(`Failed to subscribe to topic ${topic} in region ${region}: ${err}`)
//             );
//     });
// }

async function checkIsStationaryCoordinate(collection, latitude, longitude) {
    const WINDOW_SIZE = 4;
    const DISTANCE_THRESHOLD = 50;
    const STATIONARY_CHECK_MINUTES = 5;

    const minutesAgo = new Date(Date.now() - (STATIONARY_CHECK_MINUTES * 60 * 1000));
    const lastPoints = await collection
        .find({ timestamp: { $gt: minutesAgo } })
        .limit(WINDOW_SIZE);

    const distances = await Promise.all(lastPoints.map(point =>
        calculateDistanceInMeters(
            latitude,
            longitude,
            point.latitude,
            point.longitude
        )
    ));

    const isStationary = distances.every(distance => {
        return distance <= DISTANCE_THRESHOLD;
    });

    return isStationary;
}

async function getOnboardVesselId(unitId) {
    try {
        if (!unitId) throw new Error('[getOnboardVesselId] unit_id is required');
        if (!unitId) throw new Error('[getOnboardVesselId] unit_id is required');
        const vessel = await db.qmShared.collection('vessels').findOne({ unit_id: unitId });
        return vessel ? vessel._id : null;
    } catch (error) {
        throw error;
    }
}

const lastReadings = {}

async function processMessage(topic, message, region) {
    if (!topic.endsWith('/gps/status')) return;
    // console.log('[GPS Service] processing message from topic', topic, 'in region', region)
    const vesselName = topic.split('/').shift();

    const logger = createLoggerWithPath(`mqtt/${region}/${vesselName}`);

    try {
        const decoder = new TextDecoder('utf-8');
        const messageString = decoder.decode(message);
        const data = JSON.parse(messageString);

        if (data.valid_gnss_fix) {
            const { latitude, longitude, ground_speed: groundSpeed, heading_motion: headingMotion, accuracy_heading: accuracyHeading } = data;

            io.emit(`${vesselName}/gps`, {
                vesselName,
                latitude,
                longitude,
                groundSpeed,
                headingMotion,
                accuracyHeading,
                timestamp: Number(data.header.stamp.sec) * 1000,
                metadata: data
            });

            if (process.env.NODE_ENV === 'prod') {
                await thingsboardService.processSensorData({
                    deviceName: vesselName,
                    telemetry: {
                        gps_latitude: latitude,
                        gps_longitude: longitude,
                        gps_ground_speed: groundSpeed,
                        gps_heading_motion: headingMotion,
                        gps_accuracy_heading: accuracyHeading,
                        gps_timestamp: new Date(Number(data.header.stamp.sec) * 1000).toISOString()
                    }
                })
            }

            const lastReading = lastReadings[vesselName];
            if (lastReading && lastReading.getTime() + locationUpdateInterval > new Date().getTime()) return;

            lastReadings[vesselName] = new Date();

            const collection = getLocationCollection(vesselName);
            if (!collection) return logger.error(`[GPS Service] Unable to find collection for ${vesselName}`);

            /** optimized to use local variable instead of querying db every invocation */
            // const lastRecord = await collection.findOne({
            //     timestamp: { $gt: new Date().getTime() - locationUpdateInterval },
            // });
            // if (lastRecord) return;

            const isStationary = await checkIsStationaryCoordinate(collection, latitude, longitude);

            const onboardVesselId = await getOnboardVesselId(vesselName);


            const locationData = {
                latitude,
                longitude,
                groundSpeed,
                isStationary,
                headingMotion,
                accuracyHeading,
                onboardVesselId,
                metadata: data
            };

            collection.create(locationData)
                .then((res) => {
                    io.emit(`${vesselName}_location/insert`, { vesselName, ...res.toObject() })
                }).catch((error) => logger.error(`[GPS Service] Error creating location record for Vessel ${vesselName} in Region ${region}`, JSON.stringify(error)));

            logger.info(`[GPS Service] Received GPS Coordinates for Vessel ${vesselName} in Region ${region}: Latitude = ${latitude}, Longitude = ${longitude}, Ground Speed = ${groundSpeed}, Is Stationary = ${isStationary}, Heading Motion = ${headingMotion}, Accuracy Heading = ${accuracyHeading}`);
        } else {
            logger.error(`[GPS Service] Received Invalid GPS Coordinates for Vessel ${vesselName} in Region ${region}: Latitude = ${data.latitude}, Longitude = ${data.longitude}`);
        }
    } catch (error) {
        console.error(`[GPS Service] Custom Error Vessel ${vesselName} in Region ${region}`, error);
        logger.error(`[GPS Service] Error processing MQTT message for Vessel ${vesselName} in Region ${region}`, JSON.stringify(error));
    }
}
