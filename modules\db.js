const mongoose = require('mongoose');

mongoose.set('strictQuery', false)

const db = {
    qm: mongoose.createConnection(process.env.MONGO_URI, { dbName: 'quartermaster' }),
    qmai: mongoose.createConnection(process.env.MONGO_URI, { dbName: 'artifact_processor' }),
    qmShared: mongoose.createConnection(process.env.MONGO_URI, { dbName: 'quartermaster-shared' }),
    qmLocations: mongoose.createConnection(process.env.MONGO_URI, { dbName: process.env.NODE_ENV === 'prod' ? 'locations' : 'locations-local' }),
}

db.qm.on('open', () => console.log('DB connected to Quartermaster'))
db.qmai.on('open', () => console.log('DB connected to QMAI'))
db.qmShared.on('open', () => console.log('DB connected to Quartermaster-Shared'));
db.qmLocations.on('open', () => console.log('DB connected to Locations'));

db.qm.on('error', (err) => console.error(err))
db.qmai.on('error', (err) => console.error(err))
db.qmShared.on('error', (err) => console.error(err));
db.qmLocations.on('error', (err) => console.error(err));

module.exports = db